import { Platform } from 'react-native';
import BluetoothService, { BluetoothDevice } from './BluetoothService';
import WiFiDirectService, { WiFiDirectPeer } from './WiFiDirectService';
import * as Network from 'expo-network';

export type ConnectionMethod = 'bluetooth' | 'wifi-direct' | 'lan' | 'auto';

export interface ConnectedDevice {
  id: string;
  name: string;
  address: string;
  method: ConnectionMethod;
  connected: boolean;
  lastSeen: Date;
  signalStrength?: number;
}

export interface ConnectionStatus {
  isConnected: boolean;
  method: ConnectionMethod;
  deviceCount: number;
  quality: 'excellent' | 'good' | 'fair' | 'poor';
  latency?: number;
}

export interface ConnectionManagerInterface {
  initialize(): Promise<boolean>;
  startDiscovery(method?: ConnectionMethod): Promise<void>;
  stopDiscovery(): Promise<void>;
  connectToDevice(deviceId: string, method?: ConnectionMethod): Promise<boolean>;
  disconnectFromDevice(deviceId: string): Promise<boolean>;
  disconnectAll(): Promise<void>;
  getAvailableDevices(): Promise<ConnectedDevice[]>;
  getConnectedDevices(): ConnectedDevice[];
  getConnectionStatus(): ConnectionStatus;
  sendData(deviceId: string, data: any): Promise<boolean>;
  broadcastData(data: any): Promise<boolean>;
  onDeviceDiscovered(callback: (device: ConnectedDevice) => void): void;
  onDeviceConnected(callback: (device: ConnectedDevice) => void): void;
  onDeviceDisconnected(callback: (device: ConnectedDevice) => void): void;
  onDataReceived(callback: (deviceId: string, data: any) => void): void;
  onConnectionStatusChanged(callback: (status: ConnectionStatus) => void): void;
}

class ConnectionManager implements ConnectionManagerInterface {
  private connectedDevices: Map<string, ConnectedDevice> = new Map();
  private availableDevices: Map<string, ConnectedDevice> = new Map();
  private currentMethod: ConnectionMethod = 'auto';
  private isInitialized = false;
  
  // Event callbacks
  private deviceDiscoveredCallback?: (device: ConnectedDevice) => void;
  private deviceConnectedCallback?: (device: ConnectedDevice) => void;
  private deviceDisconnectedCallback?: (device: ConnectedDevice) => void;
  private dataReceivedCallback?: (deviceId: string, data: any) => void;
  private connectionStatusChangedCallback?: (status: ConnectionStatus) => void;

  constructor() {
    this.setupServiceListeners();
  }

  private setupServiceListeners(): void {
    // Bluetooth service listeners
    BluetoothService.onDeviceDiscovered((device: BluetoothDevice) => {
      const connectedDevice: ConnectedDevice = {
        id: device.id,
        name: device.name,
        address: device.address,
        method: 'bluetooth',
        connected: device.connected,
        lastSeen: new Date(),
        signalStrength: device.rssi,
      };
      
      this.availableDevices.set(device.id, connectedDevice);
      this.deviceDiscoveredCallback?.(connectedDevice);
    });

    BluetoothService.onDeviceConnected((device: BluetoothDevice) => {
      const connectedDevice: ConnectedDevice = {
        id: device.id,
        name: device.name,
        address: device.address,
        method: 'bluetooth',
        connected: true,
        lastSeen: new Date(),
      };
      
      this.connectedDevices.set(device.id, connectedDevice);
      this.deviceConnectedCallback?.(connectedDevice);
      this.notifyConnectionStatusChanged();
    });

    BluetoothService.onDeviceDisconnected((device: BluetoothDevice) => {
      const connectedDevice = this.connectedDevices.get(device.id);
      if (connectedDevice) {
        connectedDevice.connected = false;
        this.connectedDevices.delete(device.id);
        this.deviceDisconnectedCallback?.(connectedDevice);
        this.notifyConnectionStatusChanged();
      }
    });

    BluetoothService.onDataReceived((deviceId: string, data: string) => {
      try {
        const parsedData = JSON.parse(data);
        this.dataReceivedCallback?.(deviceId, parsedData);
      } catch (error) {
        this.dataReceivedCallback?.(deviceId, data);
      }
    });

    // WiFi Direct service listeners (Android only)
    if (Platform.OS === 'android') {
      WiFiDirectService.onPeersChanged((peers: WiFiDirectPeer[]) => {
        peers.forEach(peer => {
          const connectedDevice: ConnectedDevice = {
            id: peer.deviceAddress,
            name: peer.deviceName,
            address: peer.deviceAddress,
            method: 'wifi-direct',
            connected: false,
            lastSeen: new Date(),
          };
          
          this.availableDevices.set(peer.deviceAddress, connectedDevice);
          this.deviceDiscoveredCallback?.(connectedDevice);
        });
      });

      WiFiDirectService.onConnectionChanged((connectionInfo: any) => {
        if (connectionInfo.isConnected) {
          // Handle WiFi Direct connection
          this.notifyConnectionStatusChanged();
        }
      });

      WiFiDirectService.onDataReceived((data: string) => {
        try {
          const parsedData = JSON.parse(data);
          this.dataReceivedCallback?.('wifi-direct', parsedData);
        } catch (error) {
          this.dataReceivedCallback?.('wifi-direct', data);
        }
      });
    }
  }

  async initialize(): Promise<boolean> {
    try {
      // Initialize Bluetooth
      const bluetoothPermissions = await BluetoothService.requestPermissions();
      if (!bluetoothPermissions) {
        console.warn('Bluetooth permissions not granted');
      }

      // Initialize WiFi Direct (Android only)
      if (Platform.OS === 'android') {
        const wifiDirectPermissions = await WiFiDirectService.requestPermissions();
        if (!wifiDirectPermissions) {
          console.warn('WiFi Direct permissions not granted');
        }
        
        const wifiDirectInitialized = await WiFiDirectService.initialize();
        if (!wifiDirectInitialized) {
          console.warn('WiFi Direct initialization failed');
        }
      }

      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Error initializing ConnectionManager:', error);
      return false;
    }
  }

  async startDiscovery(method: ConnectionMethod = 'auto'): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('ConnectionManager not initialized');
    }

    this.currentMethod = method;
    this.availableDevices.clear();

    try {
      if (method === 'bluetooth' || method === 'auto') {
        const bluetoothEnabled = await BluetoothService.isEnabled();
        if (bluetoothEnabled) {
          await BluetoothService.startDiscovery();
        } else {
          const enabled = await BluetoothService.requestEnable();
          if (enabled) {
            await BluetoothService.startDiscovery();
          }
        }
      }

      if ((method === 'wifi-direct' || method === 'auto') && Platform.OS === 'android') {
        if (WiFiDirectService.isAvailable()) {
          await WiFiDirectService.startPeerDiscovery();
        }
      }
    } catch (error) {
      console.error('Error starting discovery:', error);
      throw error;
    }
  }

  async stopDiscovery(): Promise<void> {
    try {
      await BluetoothService.stopDiscovery();
      
      if (Platform.OS === 'android' && WiFiDirectService.isAvailable()) {
        await WiFiDirectService.stopPeerDiscovery();
      }
    } catch (error) {
      console.error('Error stopping discovery:', error);
      throw error;
    }
  }

  async connectToDevice(deviceId: string, method?: ConnectionMethod): Promise<boolean> {
    const device = this.availableDevices.get(deviceId);
    if (!device) {
      return false;
    }

    const connectionMethod = method || device.method;

    try {
      let success = false;

      switch (connectionMethod) {
        case 'bluetooth':
          success = await BluetoothService.connectToDevice(deviceId);
          break;
        case 'wifi-direct':
          if (Platform.OS === 'android') {
            success = await WiFiDirectService.connectToPeer(device.address);
          }
          break;
        default:
          return false;
      }

      if (success) {
        device.connected = true;
        device.method = connectionMethod;
        this.connectedDevices.set(deviceId, device);
        this.notifyConnectionStatusChanged();
      }

      return success;
    } catch (error) {
      console.error('Error connecting to device:', error);
      return false;
    }
  }

  async disconnectFromDevice(deviceId: string): Promise<boolean> {
    const device = this.connectedDevices.get(deviceId);
    if (!device) {
      return false;
    }

    try {
      let success = false;

      switch (device.method) {
        case 'bluetooth':
          success = await BluetoothService.disconnectFromDevice(deviceId);
          break;
        case 'wifi-direct':
          if (Platform.OS === 'android') {
            success = await WiFiDirectService.disconnect();
          }
          break;
        default:
          return false;
      }

      if (success) {
        device.connected = false;
        this.connectedDevices.delete(deviceId);
        this.notifyConnectionStatusChanged();
      }

      return success;
    } catch (error) {
      console.error('Error disconnecting from device:', error);
      return false;
    }
  }

  async disconnectAll(): Promise<void> {
    const disconnectPromises = Array.from(this.connectedDevices.keys()).map(
      deviceId => this.disconnectFromDevice(deviceId)
    );
    
    await Promise.all(disconnectPromises);
  }

  async getAvailableDevices(): Promise<ConnectedDevice[]> {
    return Array.from(this.availableDevices.values());
  }

  getConnectedDevices(): ConnectedDevice[] {
    return Array.from(this.connectedDevices.values());
  }

  getConnectionStatus(): ConnectionStatus {
    const deviceCount = this.connectedDevices.size;
    const isConnected = deviceCount > 0;
    
    // Determine connection quality based on device count and signal strength
    let quality: 'excellent' | 'good' | 'fair' | 'poor' = 'poor';
    if (deviceCount > 0) {
      const avgSignalStrength = Array.from(this.connectedDevices.values())
        .filter(device => device.signalStrength !== undefined)
        .reduce((sum, device) => sum + (device.signalStrength || 0), 0) / deviceCount;
      
      if (avgSignalStrength > -50) quality = 'excellent';
      else if (avgSignalStrength > -70) quality = 'good';
      else if (avgSignalStrength > -85) quality = 'fair';
    }

    return {
      isConnected,
      method: this.currentMethod,
      deviceCount,
      quality,
    };
  }

  async sendData(deviceId: string, data: any): Promise<boolean> {
    const device = this.connectedDevices.get(deviceId);
    if (!device) {
      return false;
    }

    try {
      const serializedData = JSON.stringify(data);

      switch (device.method) {
        case 'bluetooth':
          return await BluetoothService.sendData(deviceId, serializedData);
        case 'wifi-direct':
          if (Platform.OS === 'android') {
            return await WiFiDirectService.sendData(serializedData);
          }
          break;
      }

      return false;
    } catch (error) {
      console.error('Error sending data:', error);
      return false;
    }
  }

  async broadcastData(data: any): Promise<boolean> {
    const sendPromises = Array.from(this.connectedDevices.keys()).map(
      deviceId => this.sendData(deviceId, data)
    );
    
    const results = await Promise.all(sendPromises);
    return results.some(result => result);
  }

  private notifyConnectionStatusChanged(): void {
    const status = this.getConnectionStatus();
    this.connectionStatusChangedCallback?.(status);
  }

  onDeviceDiscovered(callback: (device: ConnectedDevice) => void): void {
    this.deviceDiscoveredCallback = callback;
  }

  onDeviceConnected(callback: (device: ConnectedDevice) => void): void {
    this.deviceConnectedCallback = callback;
  }

  onDeviceDisconnected(callback: (device: ConnectedDevice) => void): void {
    this.deviceDisconnectedCallback = callback;
  }

  onDataReceived(callback: (deviceId: string, data: any) => void): void {
    this.dataReceivedCallback = callback;
  }

  onConnectionStatusChanged(callback: (status: ConnectionStatus) => void): void {
    this.connectionStatusChangedCallback = callback;
  }
}

export default new ConnectionManager();
