import { Platform } from 'react-native';
import { GameInfo } from './GameDetector';

export interface CompatibilityCheck {
  isCompatible: boolean;
  score: number; // 0-100, higher is better
  issues: CompatibilityIssue[];
  recommendations: string[];
  requirements: GameRequirements;
}

export interface CompatibilityIssue {
  type: 'error' | 'warning' | 'info';
  category: 'network' | 'device' | 'version' | 'players' | 'permissions';
  message: string;
  solution?: string;
}

export interface GameRequirements {
  minPlayers: number;
  maxPlayers: number;
  networkTypes: ('wifi' | 'bluetooth' | 'cellular')[];
  platformSupport: ('ios' | 'android')[];
  minVersion?: string;
  permissions: string[];
  features: string[];
}

export interface SessionRequirements {
  playerCount: number;
  networkType: 'wifi' | 'bluetooth' | 'cellular' | 'local';
  devicePlatforms: string[];
  gameVersions: { [deviceId: string]: string };
}

class GameCompatibility {
  private static instance: GameCompatibility;
  private compatibilityRules: Map<string, GameRequirements> = new Map();

  static getInstance(): GameCompatibility {
    if (!GameCompatibility.instance) {
      GameCompatibility.instance = new GameCompatibility();
    }
    return GameCompatibility.instance;
  }

  constructor() {
    this.initializeCompatibilityRules();
  }

  private initializeCompatibilityRules() {
    // Define compatibility rules for known games
    this.compatibilityRules.set('com.mojang.minecraftpe', {
      minPlayers: 1,
      maxPlayers: 8,
      networkTypes: ['wifi', 'bluetooth'],
      platformSupport: ['ios', 'android'],
      minVersion: '1.19.0',
      permissions: ['INTERNET', 'ACCESS_NETWORK_STATE', 'BLUETOOTH'],
      features: ['cross-platform', 'world-sync', 'voice-chat'],
    });

    this.compatibilityRules.set('com.innersloth.spacemafia', {
      minPlayers: 4,
      maxPlayers: 15,
      networkTypes: ['wifi', 'bluetooth'],
      platformSupport: ['ios', 'android'],
      minVersion: '2023.3.28',
      permissions: ['INTERNET', 'ACCESS_NETWORK_STATE'],
      features: ['cross-platform', 'text-chat'],
    });

    this.compatibilityRules.set('com.chess.com', {
      minPlayers: 2,
      maxPlayers: 2,
      networkTypes: ['wifi', 'bluetooth', 'cellular'],
      platformSupport: ['ios', 'android'],
      permissions: ['INTERNET'],
      features: ['cross-platform', 'turn-based'],
    });

    this.compatibilityRules.set('com.ea.game.pvzfree_row', {
      minPlayers: 1,
      maxPlayers: 2,
      networkTypes: ['bluetooth'],
      platformSupport: ['ios', 'android'],
      permissions: ['BLUETOOTH', 'BLUETOOTH_ADMIN'],
      features: ['local-only', 'real-time'],
    });

    this.compatibilityRules.set('com.scopely.monopolygo', {
      minPlayers: 2,
      maxPlayers: 6,
      networkTypes: ['wifi', 'bluetooth'],
      platformSupport: ['ios', 'android'],
      permissions: ['BLUETOOTH', 'ACCESS_NETWORK_STATE'],
      features: ['turn-based', 'save-state'],
    });
  }

  async checkGameCompatibility(
    game: GameInfo,
    sessionRequirements: SessionRequirements
  ): Promise<CompatibilityCheck> {
    const rules = this.compatibilityRules.get(game.packageName);
    
    if (!rules) {
      return this.createUnknownGameCompatibility(game, sessionRequirements);
    }

    const issues: CompatibilityIssue[] = [];
    const recommendations: string[] = [];
    let score = 100;

    // Check player count compatibility
    const playerCheck = this.checkPlayerCount(rules, sessionRequirements);
    if (!playerCheck.isValid) {
      issues.push(...playerCheck.issues);
      score -= playerCheck.penalty;
    }

    // Check network compatibility
    const networkCheck = this.checkNetworkCompatibility(rules, sessionRequirements);
    if (!networkCheck.isValid) {
      issues.push(...networkCheck.issues);
      score -= networkCheck.penalty;
    }

    // Check platform compatibility
    const platformCheck = this.checkPlatformCompatibility(rules, sessionRequirements);
    if (!platformCheck.isValid) {
      issues.push(...platformCheck.issues);
      score -= platformCheck.penalty;
    }

    // Check version compatibility
    const versionCheck = this.checkVersionCompatibility(rules, sessionRequirements);
    if (!versionCheck.isValid) {
      issues.push(...versionCheck.issues);
      score -= versionCheck.penalty;
    }

    // Generate recommendations
    recommendations.push(...this.generateRecommendations(game, rules, issues));

    const isCompatible = issues.filter(issue => issue.type === 'error').length === 0;

    return {
      isCompatible,
      score: Math.max(0, score),
      issues,
      recommendations,
      requirements: rules,
    };
  }

  private checkPlayerCount(
    rules: GameRequirements,
    session: SessionRequirements
  ): { isValid: boolean; issues: CompatibilityIssue[]; penalty: number } {
    const issues: CompatibilityIssue[] = [];
    let penalty = 0;

    if (session.playerCount < rules.minPlayers) {
      issues.push({
        type: 'error',
        category: 'players',
        message: `Not enough players. Need at least ${rules.minPlayers}, have ${session.playerCount}`,
        solution: `Invite ${rules.minPlayers - session.playerCount} more player(s)`,
      });
      penalty = 50;
    } else if (session.playerCount > rules.maxPlayers) {
      issues.push({
        type: 'error',
        category: 'players',
        message: `Too many players. Maximum ${rules.maxPlayers}, have ${session.playerCount}`,
        solution: `Remove ${session.playerCount - rules.maxPlayers} player(s)`,
      });
      penalty = 30;
    }

    return { isValid: issues.length === 0, issues, penalty };
  }

  private checkNetworkCompatibility(
    rules: GameRequirements,
    session: SessionRequirements
  ): { isValid: boolean; issues: CompatibilityIssue[]; penalty: number } {
    const issues: CompatibilityIssue[] = [];
    let penalty = 0;

    if (!rules.networkTypes.includes(session.networkType as any)) {
      issues.push({
        type: 'error',
        category: 'network',
        message: `Network type '${session.networkType}' not supported`,
        solution: `Use one of: ${rules.networkTypes.join(', ')}`,
      });
      penalty = 40;
    }

    // Check for optimal network type
    if (session.networkType === 'cellular' && rules.networkTypes.includes('wifi')) {
      issues.push({
        type: 'warning',
        category: 'network',
        message: 'WiFi recommended for better performance',
        solution: 'Connect to WiFi for optimal experience',
      });
      penalty = 10;
    }

    return { isValid: issues.filter(i => i.type === 'error').length === 0, issues, penalty };
  }

  private checkPlatformCompatibility(
    rules: GameRequirements,
    session: SessionRequirements
  ): { isValid: boolean; issues: CompatibilityIssue[]; penalty: number } {
    const issues: CompatibilityIssue[] = [];
    let penalty = 0;

    const unsupportedPlatforms = session.devicePlatforms.filter(
      platform => !rules.platformSupport.includes(platform as any)
    );

    if (unsupportedPlatforms.length > 0) {
      issues.push({
        type: 'error',
        category: 'device',
        message: `Unsupported platforms: ${unsupportedPlatforms.join(', ')}`,
        solution: 'All players need compatible devices',
      });
      penalty = 30;
    }

    // Check for mixed platforms (might have compatibility issues)
    const uniquePlatforms = new Set(session.devicePlatforms);
    if (uniquePlatforms.size > 1) {
      issues.push({
        type: 'info',
        category: 'device',
        message: 'Mixed platforms detected (iOS/Android)',
        solution: 'Cross-platform play may have limitations',
      });
      penalty = 5;
    }

    return { isValid: issues.filter(i => i.type === 'error').length === 0, issues, penalty };
  }

  private checkVersionCompatibility(
    rules: GameRequirements,
    session: SessionRequirements
  ): { isValid: boolean; issues: CompatibilityIssue[]; penalty: number } {
    const issues: CompatibilityIssue[] = [];
    let penalty = 0;

    if (rules.minVersion) {
      const versions = Object.values(session.gameVersions);
      const outdatedVersions = versions.filter(version => 
        this.compareVersions(version, rules.minVersion!) < 0
      );

      if (outdatedVersions.length > 0) {
        issues.push({
          type: 'warning',
          category: 'version',
          message: `Some players have outdated game versions`,
          solution: 'Update game to latest version for best compatibility',
        });
        penalty = 15;
      }
    }

    // Check for version mismatches
    const versions = Object.values(session.gameVersions);
    const uniqueVersions = new Set(versions);
    if (uniqueVersions.size > 1) {
      issues.push({
        type: 'warning',
        category: 'version',
        message: 'Players have different game versions',
        solution: 'Ensure all players have the same game version',
      });
      penalty = 10;
    }

    return { isValid: issues.filter(i => i.type === 'error').length === 0, issues, penalty };
  }

  private generateRecommendations(
    game: GameInfo,
    rules: GameRequirements,
    issues: CompatibilityIssue[]
  ): string[] {
    const recommendations: string[] = [];

    // Add game-specific recommendations
    if (rules.features.includes('voice-chat')) {
      recommendations.push('Enable microphone permissions for voice chat');
    }

    if (rules.features.includes('cross-platform')) {
      recommendations.push('Cross-platform play supported - invite friends on any device');
    }

    if (rules.networkTypes.includes('bluetooth') && rules.networkTypes.length === 1) {
      recommendations.push('Ensure Bluetooth is enabled and devices are close together');
    }

    if (rules.features.includes('turn-based')) {
      recommendations.push('Turn-based game - connection stability less critical');
    }

    // Add issue-specific recommendations
    const hasNetworkIssues = issues.some(i => i.category === 'network');
    if (hasNetworkIssues) {
      recommendations.push('Check network settings and try different connection methods');
    }

    return recommendations;
  }

  private createUnknownGameCompatibility(
    game: GameInfo,
    session: SessionRequirements
  ): CompatibilityCheck {
    const issues: CompatibilityIssue[] = [{
      type: 'warning',
      category: 'version',
      message: 'Game compatibility unknown',
      solution: 'Test connection manually to verify compatibility',
    }];

    return {
      isCompatible: true,
      score: 70, // Neutral score for unknown games
      issues,
      recommendations: [
        'This game is not in our compatibility database',
        'Try connecting and report any issues to help improve compatibility',
      ],
      requirements: {
        minPlayers: 1,
        maxPlayers: 10,
        networkTypes: ['wifi', 'bluetooth'],
        platformSupport: ['ios', 'android'],
        permissions: [],
        features: [],
      },
    };
  }

  private compareVersions(version1: string, version2: string): number {
    const v1Parts = version1.split('.').map(Number);
    const v2Parts = version2.split('.').map(Number);
    
    const maxLength = Math.max(v1Parts.length, v2Parts.length);
    
    for (let i = 0; i < maxLength; i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;
      
      if (v1Part < v2Part) return -1;
      if (v1Part > v2Part) return 1;
    }
    
    return 0;
  }

  async getOptimalNetworkType(
    game: GameInfo,
    availableNetworks: string[]
  ): Promise<string | null> {
    const rules = this.compatibilityRules.get(game.packageName);
    if (!rules) return availableNetworks[0] || null;

    // Find the best available network type
    const preferredOrder = ['wifi', 'bluetooth', 'cellular'];
    
    for (const networkType of preferredOrder) {
      if (rules.networkTypes.includes(networkType as any) && 
          availableNetworks.includes(networkType)) {
        return networkType;
      }
    }

    return null;
  }

  async getCompatibilityScore(
    game: GameInfo,
    session: SessionRequirements
  ): Promise<number> {
    const check = await this.checkGameCompatibility(game, session);
    return check.score;
  }

  async isGameSupported(packageName: string): Promise<boolean> {
    return this.compatibilityRules.has(packageName);
  }

  async getSupportedGames(): Promise<string[]> {
    return Array.from(this.compatibilityRules.keys());
  }
}

export default GameCompatibility.getInstance();
