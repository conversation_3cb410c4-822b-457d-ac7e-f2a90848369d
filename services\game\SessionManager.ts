import { GameInfo } from "./GameDetector";
import { CompatibilityCheck } from "./GameCompatibility";

// React Native compatible EventEmitter implementation
class SimpleEventEmitter {
  private listeners: { [event: string]: Function[] } = {};

  on(event: string, listener: Function): void {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(listener);
  }

  emit(event: string, ...args: any[]): void {
    if (this.listeners[event]) {
      this.listeners[event].forEach((listener) => {
        try {
          listener(...args);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  off(event: string, listener: Function): void {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(
        (l) => l !== listener
      );
    }
  }

  removeAllListeners(event?: string): void {
    if (event) {
      delete this.listeners[event];
    } else {
      this.listeners = {};
    }
  }
}

export interface GameSession {
  id: string;
  name: string;
  game: GameInfo;
  host: Player;
  players: Player[];
  maxPlayers: number;
  status: "waiting" | "starting" | "active" | "paused" | "ended";
  settings: SessionSettings;
  createdAt: number;
  startedAt?: number;
  endedAt?: number;
  networkType: "wifi" | "bluetooth" | "local";
  connectionInfo: ConnectionInfo;
  compatibility?: CompatibilityCheck;
}

export interface Player {
  id: string;
  name: string;
  deviceType: "ios" | "android";
  gameVersion: string;
  isHost: boolean;
  isReady: boolean;
  connectionStatus: "connected" | "connecting" | "disconnected";
  lastSeen: number;
  avatar?: string;
  stats?: PlayerStats;
}

export interface PlayerStats {
  gamesPlayed: number;
  gamesWon: number;
  totalPlaytime: number;
  favoriteGames: string[];
}

export interface SessionSettings {
  isPrivate: boolean;
  password?: string;
  allowSpectators: boolean;
  maxSpectators: number;
  autoStart: boolean;
  gameMode?: string;
  difficulty?: string;
  customRules?: { [key: string]: any };
}

export interface ConnectionInfo {
  hostIP?: string;
  port?: number;
  bluetoothId?: string;
  networkName?: string;
  securityToken: string;
}

export interface SessionInvite {
  sessionId: string;
  sessionName: string;
  hostName: string;
  gameName: string;
  playerCount: number;
  maxPlayers: number;
  expiresAt: number;
  connectionData: string;
}

class SessionManager extends SimpleEventEmitter {
  private static instance: SessionManager;
  private activeSessions: Map<string, GameSession> = new Map();
  private currentSession: GameSession | null = null;
  private sessionHistory: GameSession[] = [];

  static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  async createSession(
    game: GameInfo,
    host: Player,
    settings: Partial<SessionSettings> = {}
  ): Promise<GameSession> {
    const sessionId = this.generateSessionId();

    const defaultSettings: SessionSettings = {
      isPrivate: false,
      allowSpectators: true,
      maxSpectators: 10,
      autoStart: false,
      ...settings,
    };

    const session: GameSession = {
      id: sessionId,
      name: `${host.name}'s ${game.name} Session`,
      game,
      host,
      players: [{ ...host, isHost: true, isReady: true }],
      maxPlayers: game.requirements?.maxPlayers || 8,
      status: "waiting",
      settings: defaultSettings,
      createdAt: Date.now(),
      networkType: "wifi", // Default, can be changed
      connectionInfo: {
        securityToken: this.generateSecurityToken(),
      },
    };

    this.activeSessions.set(sessionId, session);
    this.currentSession = session;

    this.emit("sessionCreated", session);
    return session;
  }

  async joinSession(sessionId: string, player: Player): Promise<boolean> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    if (session.status !== "waiting") {
      throw new Error("Session is not accepting new players");
    }

    if (session.players.length >= session.maxPlayers) {
      throw new Error("Session is full");
    }

    // Check if player is already in session
    const existingPlayer = session.players.find((p) => p.id === player.id);
    if (existingPlayer) {
      throw new Error("Player already in session");
    }

    // Add player to session
    const newPlayer: Player = {
      ...player,
      isHost: false,
      isReady: false,
      connectionStatus: "connected",
      lastSeen: Date.now(),
    };

    session.players.push(newPlayer);
    this.currentSession = session;

    this.emit("playerJoined", { session, player: newPlayer });

    // Check if auto-start is enabled and all players are ready
    if (session.settings.autoStart && this.areAllPlayersReady(session)) {
      await this.startSession(sessionId);
    }

    return true;
  }

  async leaveSession(sessionId: string, playerId: string): Promise<boolean> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      return false;
    }

    const playerIndex = session.players.findIndex((p) => p.id === playerId);
    if (playerIndex === -1) {
      return false;
    }

    const player = session.players[playerIndex];
    session.players.splice(playerIndex, 1);

    this.emit("playerLeft", { session, player });

    // If host left, transfer host to another player or end session
    if (player.isHost) {
      if (session.players.length > 0) {
        session.players[0].isHost = true;
        session.host = session.players[0];
        this.emit("hostTransferred", { session, newHost: session.host });
      } else {
        await this.endSession(sessionId);
      }
    }

    // Clear current session if this player left
    if (
      this.currentSession?.id === sessionId &&
      this.currentSession.players.some((p) => p.id === playerId)
    ) {
      this.currentSession = null;
    }

    return true;
  }

  async startSession(sessionId: string): Promise<boolean> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    if (session.status !== "waiting") {
      throw new Error("Session cannot be started");
    }

    if (session.players.length < (session.game.requirements?.minPlayers || 1)) {
      throw new Error("Not enough players to start");
    }

    session.status = "starting";
    session.startedAt = Date.now();

    this.emit("sessionStarting", session);

    // Simulate game launch delay
    setTimeout(() => {
      session.status = "active";
      this.emit("sessionStarted", session);
    }, 3000);

    return true;
  }

  async endSession(sessionId: string): Promise<boolean> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      return false;
    }

    session.status = "ended";
    session.endedAt = Date.now();

    // Move to history
    this.sessionHistory.push(session);
    this.activeSessions.delete(sessionId);

    // Clear current session if it's this one
    if (this.currentSession?.id === sessionId) {
      this.currentSession = null;
    }

    this.emit("sessionEnded", session);
    return true;
  }

  async setPlayerReady(
    sessionId: string,
    playerId: string,
    ready: boolean
  ): Promise<boolean> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      return false;
    }

    const player = session.players.find((p) => p.id === playerId);
    if (!player) {
      return false;
    }

    player.isReady = ready;
    this.emit("playerReadyChanged", { session, player, ready });

    // Check if all players are ready and auto-start is enabled
    if (session.settings.autoStart && this.areAllPlayersReady(session)) {
      await this.startSession(sessionId);
    }

    return true;
  }

  async updateSessionSettings(
    sessionId: string,
    settings: Partial<SessionSettings>
  ): Promise<boolean> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      return false;
    }

    session.settings = { ...session.settings, ...settings };
    this.emit("sessionSettingsUpdated", { session, settings });
    return true;
  }

  async generateInvite(
    sessionId: string,
    expirationMinutes: number = 60
  ): Promise<SessionInvite> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    const connectionData = JSON.stringify({
      type: "logaco-session-invite",
      sessionId: session.id,
      hostId: session.host.id,
      connectionInfo: session.connectionInfo,
      timestamp: Date.now(),
    });

    return {
      sessionId: session.id,
      sessionName: session.name,
      hostName: session.host.name,
      gameName: session.game.name,
      playerCount: session.players.length,
      maxPlayers: session.maxPlayers,
      expiresAt: Date.now() + expirationMinutes * 60 * 1000,
      connectionData,
    };
  }

  async joinSessionFromInvite(
    inviteData: string,
    player: Player
  ): Promise<boolean> {
    try {
      const invite = JSON.parse(inviteData);

      if (invite.type !== "logaco-session-invite") {
        throw new Error("Invalid invite format");
      }

      if (Date.now() > invite.expiresAt) {
        throw new Error("Invite has expired");
      }

      return await this.joinSession(invite.sessionId, player);
    } catch (error) {
      throw new Error("Invalid or expired invite");
    }
  }

  getCurrentSession(): GameSession | null {
    return this.currentSession;
  }

  getActiveSessions(): GameSession[] {
    return Array.from(this.activeSessions.values());
  }

  getSessionHistory(): GameSession[] {
    return [...this.sessionHistory].reverse(); // Most recent first
  }

  getSession(sessionId: string): GameSession | null {
    return this.activeSessions.get(sessionId) || null;
  }

  private areAllPlayersReady(session: GameSession): boolean {
    return session.players.every((player) => player.isReady);
  }

  private generateSessionId(): string {
    return (
      "session_" +
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15)
    );
  }

  private generateSecurityToken(): string {
    return (
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15)
    );
  }

  // Utility methods for session management
  async kickPlayer(
    sessionId: string,
    playerId: string,
    hostId: string
  ): Promise<boolean> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      return false;
    }

    const host = session.players.find((p) => p.id === hostId && p.isHost);
    if (!host) {
      throw new Error("Only host can kick players");
    }

    return await this.leaveSession(sessionId, playerId);
  }

  async pauseSession(sessionId: string): Promise<boolean> {
    const session = this.activeSessions.get(sessionId);
    if (!session || session.status !== "active") {
      return false;
    }

    session.status = "paused";
    this.emit("sessionPaused", session);
    return true;
  }

  async resumeSession(sessionId: string): Promise<boolean> {
    const session = this.activeSessions.get(sessionId);
    if (!session || session.status !== "paused") {
      return false;
    }

    session.status = "active";
    this.emit("sessionResumed", session);
    return true;
  }
}

export default SessionManager.getInstance();
