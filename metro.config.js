const { getDefaultConfig } = require("expo/metro-config");

const config = getDefaultConfig(__dirname);

// Disable source maps completely to prevent <anonymous> errors
config.serializer = {
  ...config.serializer,
  createModuleIdFactory: () => (path) => {
    // Generate stable, predictable module IDs
    if (typeof path === "string") {
      // Use a hash of the path to create stable IDs
      return path.replace(/[^a-zA-Z0-9]/g, "_").substring(0, 50);
    }
    return path;
  },
};

// Disable source map generation in transformer
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    keep_fnames: true,
    mangle: {
      keep_fnames: true,
    },
  },
};

module.exports = config;
