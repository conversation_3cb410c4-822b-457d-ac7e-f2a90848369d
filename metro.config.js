const { getDefaultConfig } = require("expo/metro-config");

const config = getDefaultConfig(__dirname);

// Add resolver configuration to handle source maps better
config.resolver = {
  ...config.resolver,
  sourceExts: [
    ...(config.resolver?.sourceExts || []),
    "ts",
    "tsx",
    "js",
    "jsx",
  ],
};

// Improve source map handling
config.serializer = {
  ...config.serializer,
  createModuleIdFactory: () => (path) => {
    // Ensure we don't get anonymous paths in source maps
    if (path.includes("<anonymous>")) {
      console.warn("Anonymous path detected:", path);
      return path.replace("<anonymous>", "unknown-module");
    }
    return path;
  },
};

// Add transformer configuration for better error handling
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    keep_fnames: true,
    mangle: {
      keep_fnames: true,
    },
  },
};

module.exports = config;
