{"name": "logaco", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@reduxjs/toolkit": "^2.8.2", "expo": "~53.0.9", "expo-barcode-scanner": "^13.0.1", "expo-blur": "^14.1.4", "expo-camera": "^16.1.6", "expo-clipboard": "^7.1.4", "expo-constants": "^17.1.6", "expo-font": "^13.3.1", "expo-linear-gradient": "^14.1.4", "expo-linking": "^7.1.5", "expo-router": "^5.0.7", "expo-splash-screen": "^0.30.8", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-qrcode-svg": "^6.3.15", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-web": "^0.20.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "jest": {"preset": "react-native", "setupFilesAfterEnv": ["<rootDir>/jest-setup.js"], "testMatch": ["**/__tests__/**/*.test.{js,jsx,ts,tsx}"], "collectCoverageFrom": ["**/*.{js,jsx,ts,tsx}", "!**/node_modules/**", "!**/coverage/**", "!**/*.config.js", "!**/expo-env.d.ts"], "transformIgnorePatterns": ["node_modules/(?!(react-native|@react-native|expo|@expo|react-native-bluetooth-classic|react-native-wifi-p2p)/)"]}, "private": true}