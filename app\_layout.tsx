import { Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useFonts } from "expo-font";
import * as SplashScreen from "expo-splash-screen";
import { useEffect, useState } from "react";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { store, persistor } from "../store";
import OnboardingScreen from "../components/onboarding/OnboardingScreen";
import UserPreferences from "../services/UserPreferences";

// Prevent the splash screen from auto-hiding before asset loading is complete
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded] = useFonts({
    // Add custom fonts here if needed
  });
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState<
    boolean | null
  >(null);

  useEffect(() => {
    const checkOnboarding = async () => {
      try {
        const completed = await UserPreferences.hasCompletedOnboarding();
        setHasCompletedOnboarding(completed);
      } catch (error) {
        console.error("Failed to check onboarding status:", error);
        setHasCompletedOnboarding(false);
      }
    };

    checkOnboarding();
  }, []);

  useEffect(() => {
    if (loaded && hasCompletedOnboarding !== null) {
      SplashScreen.hideAsync();
    }
  }, [loaded, hasCompletedOnboarding]);

  const handleOnboardingComplete = async () => {
    await UserPreferences.completeOnboarding();
    setHasCompletedOnboarding(true);
  };

  if (!loaded || hasCompletedOnboarding === null) {
    return null;
  }

  if (!hasCompletedOnboarding) {
    return (
      <Provider store={store}>
        <OnboardingScreen onComplete={handleOnboardingComplete} />
      </Provider>
    );
  }

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <StatusBar style="light" backgroundColor="transparent" translucent />
        <Stack>
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen name="modal" options={{ presentation: "modal" }} />
        </Stack>
      </PersistGate>
    </Provider>
  );
}
