import { Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useFonts } from "expo-font";
import * as SplashScreen from "expo-splash-screen";
import { useEffect, useState } from "react";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { View, Text, ActivityIndicator } from "react-native";
import { store, persistor } from "../store";
import OnboardingScreen from "../components/onboarding/OnboardingScreen";
import UserPreferences from "../services/UserPreferences";

// Prevent the splash screen from auto-hiding before asset loading is complete
SplashScreen.preventAutoHideAsync().catch(console.warn);

export default function RootLayout() {
  const [loaded, error] = useFonts({
    // Add custom fonts here if needed
  });
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState<
    boolean | null
  >(null);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingError, setLoadingError] = useState<string | null>(null);

  useEffect(() => {
    const checkOnboarding = async () => {
      try {
        console.log("Checking onboarding status...");
        const completed = await UserPreferences.hasCompletedOnboarding();
        console.log("Onboarding completed:", completed);
        setHasCompletedOnboarding(completed);
        setIsLoading(false);
      } catch (error) {
        console.error("Failed to check onboarding status:", error);
        setLoadingError("Failed to load user preferences");
        setHasCompletedOnboarding(false);
        setIsLoading(false);
      }
    };

    if (loaded) {
      checkOnboarding();
    }
  }, [loaded]);

  useEffect(() => {
    if (loaded && !isLoading) {
      console.log("Hiding splash screen...");
      SplashScreen.hideAsync().catch(console.warn);
    }
  }, [loaded, isLoading]);

  const handleOnboardingComplete = async () => {
    try {
      await UserPreferences.completeOnboarding();
      setHasCompletedOnboarding(true);
    } catch (error) {
      console.error("Failed to complete onboarding:", error);
    }
  };

  // Show loading screen while fonts are loading or checking onboarding
  if (!loaded || isLoading) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: "#000",
        }}
      >
        <ActivityIndicator size="large" color="#fff" />
        <Text style={{ color: "#fff", marginTop: 16 }}>
          {!loaded ? "Loading fonts..." : "Checking preferences..."}
        </Text>
      </View>
    );
  }

  // Show error screen if there was a loading error
  if (loadingError) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: "#000",
        }}
      >
        <Text
          style={{
            color: "#ff6b6b",
            fontSize: 18,
            textAlign: "center",
            marginBottom: 16,
          }}
        >
          Error Loading App
        </Text>
        <Text
          style={{ color: "#fff", textAlign: "center", marginHorizontal: 32 }}
        >
          {loadingError}
        </Text>
      </View>
    );
  }

  // Show font loading error
  if (error) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: "#000",
        }}
      >
        <Text
          style={{
            color: "#ff6b6b",
            fontSize: 18,
            textAlign: "center",
            marginBottom: 16,
          }}
        >
          Font Loading Error
        </Text>
        <Text
          style={{ color: "#fff", textAlign: "center", marginHorizontal: 32 }}
        >
          Failed to load fonts. The app will continue with default fonts.
        </Text>
      </View>
    );
  }

  if (!hasCompletedOnboarding) {
    return (
      <Provider store={store}>
        <OnboardingScreen onComplete={handleOnboardingComplete} />
      </Provider>
    );
  }

  return (
    <Provider store={store}>
      <PersistGate
        loading={
          <View
            style={{
              flex: 1,
              justifyContent: "center",
              alignItems: "center",
              backgroundColor: "#000",
            }}
          >
            <ActivityIndicator size="large" color="#fff" />
            <Text style={{ color: "#fff", marginTop: 16 }}>
              Loading app data...
            </Text>
          </View>
        }
        persistor={persistor}
      >
        <StatusBar style="light" backgroundColor="transparent" translucent />
        <Stack>
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen name="modal" options={{ presentation: "modal" }} />
        </Stack>
      </PersistGate>
    </Provider>
  );
}
