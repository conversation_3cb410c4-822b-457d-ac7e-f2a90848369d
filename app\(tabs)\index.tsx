import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Alert,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { BlurView } from "expo-blur";
import { Ionicons } from "@expo/vector-icons";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";

import { StatusIndicator, LoadingSpinner, Modal } from "../../components/ui";
import { SessionCard } from "../../components/game";
import { QRCodeScanner, QRCodeGenerator } from "../../components/connection";
import { useSessionManager } from "../../hooks";
import UserPreferences from "../../services/UserPreferences";
import NetworkService from "../../services/NetworkService";

const { width, height } = Dimensions.get("window");

export default function HomeScreen() {
  const [deviceName, setDeviceName] = useState("");
  const [networkStatus, setNetworkStatus] = useState({
    wifi: false,
    bluetooth: false,
  });
  const [showQRScanner, setShowQRScanner] = useState(false);
  const [showQRGenerator, setShowQRGenerator] = useState(false);
  const [loading, setLoading] = useState(false);

  // Custom hooks
  const { currentSession } = useSessionManager();

  useEffect(() => {
    loadUserData();
    checkNetworkStatus();
  }, []);

  const loadUserData = async () => {
    try {
      const name = await UserPreferences.getDeviceName();
      setDeviceName(name);
    } catch (error) {
      console.error("Failed to load user data:", error);
    }
  };

  const checkNetworkStatus = async () => {
    try {
      const status = await NetworkService.getNetworkStatus();
      setNetworkStatus(status);
    } catch (error) {
      console.error("Failed to check network status:", error);
    }
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning";
    if (hour < 18) return "Good afternoon";
    return "Good evening";
  };

  const handleScanForGames = () => {
    router.push("/games");
  };

  const handleCreateHotspot = async () => {
    setLoading(true);
    try {
      // Mock hotspot creation
      await new Promise((resolve) => setTimeout(resolve, 2000));
      Alert.alert("Success", "WiFi hotspot created successfully!");
    } catch (error) {
      Alert.alert("Error", "Failed to create hotspot");
    } finally {
      setLoading(false);
    }
  };

  const handleBluetoothPair = () => {
    router.push("/connect");
  };

  const handleQRConnect = () => {
    setShowQRScanner(true);
  };

  const handleQRScan = (data: string) => {
    setShowQRScanner(false);
    Alert.alert("QR Code Scanned", `Processing connection data: ${data}`);
    // Process QR code data here
  };

  const handleStartScanning = () => {
    router.push("/connect");
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#1a1a2e", "#16213e", "#0f3460"]}
        style={styles.background}
      />

      <SafeAreaView style={styles.safeArea}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <View style={styles.header}>
            <View>
              <Text style={styles.greeting}>{getGreeting()}</Text>
              <Text style={styles.title}>LoGaCo</Text>
              {deviceName && (
                <Text style={styles.deviceName}>{deviceName}</Text>
              )}
            </View>
            <View style={styles.headerRight}>
              <StatusIndicator
                status={currentSession ? "connected" : "offline"}
                size="small"
                variant="badge"
              />
              <TouchableOpacity
                style={styles.profileButton}
                onPress={() => router.push("/settings")}
              >
                <Ionicons
                  name="person-circle-outline"
                  size={32}
                  color="#00D4FF"
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* Main Card */}
          <BlurView intensity={20} style={styles.mainCard}>
            <LinearGradient
              colors={["rgba(0, 212, 255, 0.1)", "rgba(0, 212, 255, 0.05)"]}
              style={styles.cardGradient}
            >
              <View style={styles.cardContent}>
                <View style={styles.iconContainer}>
                  <Ionicons name="game-controller" size={48} color="#00D4FF" />
                </View>
                <Text style={styles.cardTitle}>
                  {currentSession ? "Session Active" : "Ready to Connect"}
                </Text>
                <Text style={styles.cardSubtitle}>
                  {currentSession
                    ? `Playing ${currentSession.game.name} with ${currentSession.players.length} players`
                    : "Find nearby players and start gaming together"}
                </Text>
                <TouchableOpacity
                  style={styles.connectButton}
                  onPress={
                    currentSession
                      ? () => router.push("/games")
                      : handleStartScanning
                  }
                >
                  <Text style={styles.connectButtonText}>
                    {currentSession ? "View Session" : "Start Scanning"}
                  </Text>
                  <Ionicons name="arrow-forward" size={20} color="#1a1a2e" />
                </TouchableOpacity>
              </View>
            </LinearGradient>
          </BlurView>

          {/* Quick Actions */}
          <View style={styles.quickActions}>
            <Text style={styles.sectionTitle}>Quick Actions</Text>
            <View style={styles.actionsGrid}>
              <TouchableOpacity
                style={styles.actionCard}
                onPress={handleScanForGames}
              >
                <BlurView intensity={15} style={styles.actionBlur}>
                  <Ionicons name="search" size={24} color="#00D4FF" />
                  <Text style={styles.actionText}>Scan for Games</Text>
                </BlurView>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionCard}
                onPress={handleCreateHotspot}
                disabled={loading}
              >
                <BlurView intensity={15} style={styles.actionBlur}>
                  {loading ? (
                    <LoadingSpinner size="small" color="#00D4FF" />
                  ) : (
                    <Ionicons name="wifi" size={24} color="#00D4FF" />
                  )}
                  <Text style={styles.actionText}>Create Hotspot</Text>
                </BlurView>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionCard}
                onPress={handleBluetoothPair}
              >
                <BlurView intensity={15} style={styles.actionBlur}>
                  <Ionicons name="bluetooth" size={24} color="#00D4FF" />
                  <Text style={styles.actionText}>Bluetooth Pair</Text>
                </BlurView>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionCard}
                onPress={handleQRConnect}
              >
                <BlurView intensity={15} style={styles.actionBlur}>
                  <Ionicons name="qr-code" size={24} color="#00D4FF" />
                  <Text style={styles.actionText}>QR Connect</Text>
                </BlurView>
              </TouchableOpacity>
            </View>
          </View>

          {/* Recent Activity */}
          <View style={styles.recentActivity}>
            <Text style={styles.sectionTitle}>Recent Activity</Text>
            {currentSession ? (
              <SessionCard
                session={currentSession}
                variant="detailed"
                onPress={() => router.push("/games")}
              />
            ) : (
              <BlurView intensity={15} style={styles.activityCard}>
                <View style={styles.activityItem}>
                  <Ionicons name="time-outline" size={20} color="#00D4FF" />
                  <Text style={styles.activityText}>No recent connections</Text>
                </View>
              </BlurView>
            )}
          </View>

          {/* Network Status */}
          <View style={styles.networkStatus}>
            <Text style={styles.sectionTitle}>Network Status</Text>
            <BlurView intensity={15} style={styles.statusCard}>
              <View style={styles.statusRow}>
                <StatusIndicator
                  status={networkStatus.wifi ? "success" : "error"}
                  label="WiFi"
                  size="small"
                  variant="badge"
                />
                <StatusIndicator
                  status={networkStatus.bluetooth ? "success" : "error"}
                  label="Bluetooth"
                  size="small"
                  variant="badge"
                />
              </View>
            </BlurView>
          </View>
        </ScrollView>
      </SafeAreaView>

      {/* QR Scanner Modal */}
      <Modal
        visible={showQRScanner}
        onClose={() => setShowQRScanner(false)}
        variant="fullscreen"
      >
        <QRCodeScanner
          onScan={handleQRScan}
          onClose={() => setShowQRScanner(false)}
        />
      </Modal>

      {/* QR Generator Modal */}
      <Modal
        visible={showQRGenerator}
        onClose={() => setShowQRGenerator(false)}
        title="Share Connection"
        variant="center"
      >
        <QRCodeGenerator
          deviceInfo={{
            id: "device-123",
            name: deviceName || "My Device",
            type: "android",
            connectionInfo: {
              ip: "*************",
              port: 8080,
            },
          }}
          onClose={() => setShowQRGenerator(false)}
        />
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    height: height,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120, // Account for tab bar
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 30,
  },
  greeting: {
    fontSize: 16,
    color: "rgba(255, 255, 255, 0.7)",
    marginBottom: 4,
  },
  title: {
    fontSize: 32,
    fontWeight: "bold",
    color: "#FFFFFF",
  },
  deviceName: {
    fontSize: 12,
    color: "rgba(255, 255, 255, 0.5)",
    marginTop: 2,
  },
  headerRight: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  profileButton: {
    padding: 8,
  },
  mainCard: {
    marginHorizontal: 20,
    borderRadius: 24,
    overflow: "hidden",
    marginBottom: 30,
  },
  cardGradient: {
    padding: 24,
  },
  cardContent: {
    alignItems: "center",
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "rgba(0, 212, 255, 0.1)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 8,
  },
  cardSubtitle: {
    fontSize: 16,
    color: "rgba(255, 255, 255, 0.7)",
    textAlign: "center",
    marginBottom: 24,
  },
  connectButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#00D4FF",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
    gap: 8,
  },
  connectButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a2e",
  },
  quickActions: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 16,
  },
  actionsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
  },
  actionCard: {
    width: (width - 56) / 2,
    borderRadius: 16,
    overflow: "hidden",
  },
  actionBlur: {
    padding: 16,
    alignItems: "center",
    gap: 8,
  },
  actionText: {
    fontSize: 14,
    color: "#FFFFFF",
    textAlign: "center",
  },
  recentActivity: {
    paddingHorizontal: 20,
  },
  activityCard: {
    borderRadius: 16,
    overflow: "hidden",
    padding: 16,
  },
  activityItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  activityText: {
    fontSize: 16,
    color: "rgba(255, 255, 255, 0.7)",
  },
  networkStatus: {
    marginHorizontal: 20,
    marginTop: 20,
  },
  statusCard: {
    borderRadius: 16,
    overflow: "hidden",
    backgroundColor: "rgba(0, 212, 255, 0.05)",
    padding: 16,
  },
  statusRow: {
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
  },
});
