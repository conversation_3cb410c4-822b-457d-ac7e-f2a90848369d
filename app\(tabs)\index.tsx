import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Alert,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { BlurView } from "expo-blur";
import { Ionicons } from "@expo/vector-icons";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";

import { StatusIndicator, LoadingSpinner, Modal } from "../../components/ui";
import { SessionCard } from "../../components/game";
import { QRCodeScanner, QRCodeGenerator } from "../../components/connection";
import { useSessionManager } from "../../hooks";
import UserPreferences from "../../services/UserPreferences";
import NetworkService from "../../services/NetworkService";

const { width } = Dimensions.get("window");

export default function HomeScreen() {
  const [deviceName, setDeviceName] = useState("");
  const [networkStatus, setNetworkStatus] = useState({
    wifi: false,
    bluetooth: false,
  });
  const [showQRScanner, setShowQRScanner] = useState(false);
  const [showQRGenerator, setShowQRGenerator] = useState(false);
  const [loading, setLoading] = useState(false);

  // Custom hooks
  const { currentSession } = useSessionManager();

  useEffect(() => {
    loadUserData();
    checkNetworkStatus();
  }, []);

  const loadUserData = async () => {
    try {
      const name = await UserPreferences.getDeviceName();
      setDeviceName(name);
    } catch (error) {
      console.error("Failed to load user data:", error);
    }
  };

  const checkNetworkStatus = async () => {
    try {
      const status = await NetworkService.getNetworkStatus();
      setNetworkStatus(status);
    } catch (error) {
      console.error("Failed to check network status:", error);
    }
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning";
    if (hour < 18) return "Good afternoon";
    return "Good evening";
  };

  const handleScanForGames = () => {
    Alert.alert("Scan for Games", "Searching for nearby games...");
  };

  const handleCreateHotspot = async () => {
    setLoading(true);
    try {
      // Mock hotspot creation
      await new Promise((resolve) => setTimeout(resolve, 2000));
      Alert.alert("Success", "WiFi hotspot created successfully!");
    } catch (error) {
      Alert.alert("Error", "Failed to create hotspot");
    } finally {
      setLoading(false);
    }
  };

  const handleBluetoothPair = () => {
    Alert.alert("Bluetooth Pair", "Searching for Bluetooth devices...");
  };

  const handleStartScanning = () => {
    Alert.alert("Start Scanning", "Scanning for nearby devices...");
  };

  const handleQRConnect = () => {
    setShowQRScanner(true);
  };

  const handleQRScan = (data: string) => {
    setShowQRScanner(false);
    Alert.alert("QR Code Scanned", `Processing connection data: ${data}`);
    // Process QR code data here
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#1a1a2e", "#16213e", "#0f3460"]}
        style={styles.background}
      />

      <SafeAreaView style={styles.safeArea}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <Text style={styles.greeting}>{getGreeting()}</Text>
              {deviceName && (
                <Text style={styles.deviceName}>{deviceName}</Text>
              )}
            </View>
            <View style={styles.headerRight}>
              <StatusIndicator
                status={currentSession ? "connected" : "offline"}
                size="small"
                variant="badge"
              />
              <TouchableOpacity
                style={styles.settingsButton}
                onPress={() => router.push("/settings")}
              >
                <Ionicons name="settings-outline" size={24} color="#00D4FF" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Main Card */}
          <BlurView intensity={20} style={styles.mainCard}>
            <LinearGradient
              colors={["rgba(0, 212, 255, 0.1)", "rgba(0, 212, 255, 0.05)"]}
              style={styles.cardGradient}
            >
              <View style={styles.cardContent}>
                <View style={styles.cardIcon}>
                  <Ionicons name="game-controller" size={48} color="#00D4FF" />
                </View>
                <Text style={styles.cardTitle}>
                  {currentSession ? "Session Active" : "Ready to Connect"}
                </Text>
                <Text style={styles.cardSubtitle}>
                  {currentSession
                    ? `Playing ${currentSession.game.name} with ${currentSession.players.length} players`
                    : "Find nearby players and start gaming together"}
                </Text>
                <TouchableOpacity
                  style={styles.connectButton}
                  onPress={
                    currentSession
                      ? () => router.push("/games")
                      : handleStartScanning
                  }
                >
                  <Text style={styles.connectButtonText}>
                    {currentSession ? "View Session" : "Start Scanning"}
                  </Text>
                  <Ionicons name="arrow-forward" size={20} color="#1a1a2e" />
                </TouchableOpacity>
              </View>
            </LinearGradient>
          </BlurView>

          {/* Quick Actions */}
          <View style={styles.quickActions}>
            <Text style={styles.sectionTitle}>Quick Actions</Text>
            <View style={styles.actionsGrid}>
              <TouchableOpacity
                style={styles.actionCard}
                onPress={handleScanForGames}
              >
                <BlurView intensity={15} style={styles.actionBlur}>
                  <Ionicons name="search" size={24} color="#00D4FF" />
                  <Text style={styles.actionText}>Scan for Games</Text>
                </BlurView>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionCard}
                onPress={handleCreateHotspot}
                disabled={loading}
              >
                <BlurView intensity={15} style={styles.actionBlur}>
                  {loading ? (
                    <LoadingSpinner size="small" color="#00D4FF" />
                  ) : (
                    <Ionicons name="wifi" size={24} color="#00D4FF" />
                  )}
                  <Text style={styles.actionText}>Create Hotspot</Text>
                </BlurView>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionCard}
                onPress={handleBluetoothPair}
              >
                <BlurView intensity={15} style={styles.actionBlur}>
                  <Ionicons name="bluetooth" size={24} color="#00D4FF" />
                  <Text style={styles.actionText}>Bluetooth Pair</Text>
                </BlurView>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionCard}
                onPress={handleQRConnect}
              >
                <BlurView intensity={15} style={styles.actionBlur}>
                  <Ionicons name="qr-code" size={24} color="#00D4FF" />
                  <Text style={styles.actionText}>QR Connect</Text>
                </BlurView>
              </TouchableOpacity>
            </View>
          </View>

          {/* Recent Activity */}
          <View style={styles.recentActivity}>
            <Text style={styles.sectionTitle}>Recent Activity</Text>
            {currentSession ? (
              <SessionCard
                session={currentSession}
                variant="detailed"
                onPress={() => router.push("/games")}
              />
            ) : (
              <BlurView intensity={15} style={styles.activityCard}>
                <View style={styles.activityItem}>
                  <Ionicons name="time-outline" size={20} color="#00D4FF" />
                  <Text style={styles.activityText}>No recent connections</Text>
                </View>
              </BlurView>
            )}
          </View>

          {/* Network Status */}
          <View style={styles.networkStatus}>
            <Text style={styles.sectionTitle}>Network Status</Text>
            <BlurView intensity={15} style={styles.statusCard}>
              <View style={styles.statusRow}>
                <StatusIndicator
                  status={networkStatus.wifi ? "success" : "error"}
                  label="WiFi"
                  size="small"
                  variant="badge"
                />
                <StatusIndicator
                  status={networkStatus.bluetooth ? "success" : "error"}
                  label="Bluetooth"
                  size="small"
                  variant="badge"
                />
              </View>
            </BlurView>
          </View>
        </ScrollView>
      </SafeAreaView>

      {/* QR Scanner Modal */}
      <Modal
        visible={showQRScanner}
        onClose={() => setShowQRScanner(false)}
        variant="fullscreen"
      >
        <QRCodeScanner
          onScan={handleQRScan}
          onClose={() => setShowQRScanner(false)}
        />
      </Modal>

      {/* QR Generator Modal */}
      <Modal
        visible={showQRGenerator}
        onClose={() => setShowQRGenerator(false)}
        title="Share Connection"
        variant="center"
      >
        <QRCodeGenerator
          deviceInfo={{
            id: "device-123",
            name: deviceName || "My Device",
            type: "android",
            capabilities: ["wifi", "bluetooth"],
            connection: {
              ip: "*************",
              port: 8080,
            },
          }}
          onClose={() => setShowQRGenerator(false)}
        />
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 20,
  },
  headerLeft: {
    flex: 1,
  },
  headerRight: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  greeting: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 4,
  },
  deviceName: {
    fontSize: 16,
    color: "#00D4FF",
    opacity: 0.8,
  },
  settingsButton: {
    padding: 8,
  },
  mainCard: {
    borderRadius: 20,
    marginBottom: 30,
    overflow: "hidden",
  },
  cardGradient: {
    padding: 1,
  },
  cardContent: {
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    borderRadius: 19,
    padding: 30,
    alignItems: "center",
  },
  cardIcon: {
    marginBottom: 20,
  },
  cardTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#fff",
    textAlign: "center",
    marginBottom: 12,
  },
  cardSubtitle: {
    fontSize: 16,
    color: "#fff",
    opacity: 0.8,
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 30,
  },
  connectButton: {
    backgroundColor: "#00D4FF",
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  connectButtonText: {
    color: "#1a1a2e",
    fontSize: 16,
    fontWeight: "bold",
  },
  quickActions: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 16,
  },
  actionsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
  },
  actionCard: {
    flex: 1,
    minWidth: (width - 52) / 2,
    borderRadius: 15,
    overflow: "hidden",
  },
  actionBlur: {
    padding: 20,
    alignItems: "center",
    gap: 8,
  },
  actionText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
    textAlign: "center",
  },
  recentActivity: {
    marginBottom: 30,
  },
  activityCard: {
    borderRadius: 15,
    padding: 20,
  },
  activityItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  activityText: {
    color: "#fff",
    fontSize: 16,
    opacity: 0.8,
  },
  networkStatus: {
    marginBottom: 30,
  },
  statusCard: {
    borderRadius: 15,
    padding: 20,
  },
  statusRow: {
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
  },
});
