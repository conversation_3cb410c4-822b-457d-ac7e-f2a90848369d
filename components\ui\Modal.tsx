import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal as RNModal,
  Dimensions,
  Animated,
  ViewStyle,
  TextStyle,
  TouchableWithoutFeedback,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';

const { height, width } = Dimensions.get('window');

interface ModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  subtitle?: string;
  children?: React.ReactNode;
  variant?: 'center' | 'bottom' | 'fullscreen';
  showCloseButton?: boolean;
  closeOnBackdrop?: boolean;
  icon?: keyof typeof Ionicons.glyphMap;
  iconColor?: string;
  style?: ViewStyle;
  contentStyle?: ViewStyle;
  titleStyle?: TextStyle;
  subtitleStyle?: TextStyle;
  animationType?: 'slide' | 'fade' | 'none';
}

export default function Modal({
  visible,
  onClose,
  title,
  subtitle,
  children,
  variant = 'center',
  showCloseButton = true,
  closeOnBackdrop = true,
  icon,
  iconColor = '#00D4FF',
  style,
  contentStyle,
  titleStyle,
  subtitleStyle,
  animationType = 'fade',
}: ModalProps) {
  const slideAnim = useRef(new Animated.Value(height)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    if (visible) {
      if (variant === 'bottom') {
        Animated.parallel([
          Animated.timing(slideAnim, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start();
      } else {
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start();
      }
    } else {
      if (variant === 'bottom') {
        Animated.parallel([
          Animated.timing(slideAnim, {
            toValue: height,
            duration: 250,
            useNativeDriver: true,
          }),
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: 250,
            useNativeDriver: true,
          }),
        ]).start();
      } else {
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: 250,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 0.8,
            duration: 250,
            useNativeDriver: true,
          }),
        ]).start();
      }
    }
  }, [visible, variant, slideAnim, fadeAnim, scaleAnim]);

  const handleBackdropPress = () => {
    if (closeOnBackdrop) {
      onClose();
    }
  };

  const renderCloseButton = () => {
    if (!showCloseButton) return null;

    return (
      <TouchableOpacity style={styles.closeButton} onPress={onClose}>
        <Ionicons name="close" size={24} color="#FFFFFF" />
      </TouchableOpacity>
    );
  };

  const renderHeader = () => {
    if (!title && !icon) return null;

    return (
      <View style={styles.header}>
        {icon && (
          <View style={[styles.iconContainer, { backgroundColor: `${iconColor}20` }]}>
            <Ionicons name={icon} size={32} color={iconColor} />
          </View>
        )}
        {title && (
          <Text style={[styles.title, titleStyle]}>{title}</Text>
        )}
        {subtitle && (
          <Text style={[styles.subtitle, subtitleStyle]}>{subtitle}</Text>
        )}
      </View>
    );
  };

  const renderContent = () => {
    const containerStyle = [
      styles.container,
      variant === 'fullscreen' && styles.fullscreenContainer,
      style,
    ];

    const modalStyle = [
      styles.modal,
      variant === 'bottom' && styles.bottomModal,
      variant === 'fullscreen' && styles.fullscreenModal,
      contentStyle,
    ];

    const animatedStyle = {
      opacity: fadeAnim,
      transform: variant === 'bottom' 
        ? [{ translateY: slideAnim }]
        : [{ scale: scaleAnim }],
    };

    return (
      <View style={containerStyle}>
        <LinearGradient
          colors={['#1a1a2e', '#16213e', '#0f3460']}
          style={styles.background}
        />
        
        <TouchableWithoutFeedback onPress={handleBackdropPress}>
          <Animated.View style={[styles.backdrop, { opacity: fadeAnim }]} />
        </TouchableWithoutFeedback>

        <SafeAreaView style={styles.safeArea}>
          <Animated.View style={[modalStyle, animatedStyle]}>
            <BlurView intensity={20} style={styles.blurContainer}>
              <LinearGradient
                colors={['rgba(0, 212, 255, 0.1)', 'rgba(0, 212, 255, 0.05)']}
                style={styles.gradientContainer}
              >
                {renderCloseButton()}
                {renderHeader()}
                <View style={styles.content}>
                  {children}
                </View>
              </LinearGradient>
            </BlurView>
          </Animated.View>
        </SafeAreaView>
      </View>
    );
  };

  return (
    <RNModal
      visible={visible}
      transparent
      animationType={animationType}
      statusBarTranslucent
      onRequestClose={onClose}
    >
      {renderContent()}
    </RNModal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  fullscreenContainer: {
    margin: 0,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    height: height,
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  safeArea: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modal: {
    width: width - 40,
    maxHeight: height * 0.8,
    borderRadius: 24,
    overflow: 'hidden',
  },
  bottomModal: {
    position: 'absolute',
    bottom: 0,
    width: width,
    maxHeight: height * 0.9,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  fullscreenModal: {
    width: width,
    height: height,
    borderRadius: 0,
  },
  blurContainer: {
    flex: 1,
  },
  gradientContainer: {
    flex: 1,
    padding: 24,
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    lineHeight: 22,
  },
  content: {
    flex: 1,
  },
});
