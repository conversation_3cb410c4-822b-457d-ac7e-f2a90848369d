import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  FlatList,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import Button from '../ui/Button';

const { width, height } = Dimensions.get('window');

interface OnboardingStep {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
  features: string[];
}

interface OnboardingScreenProps {
  onComplete: () => void;
}

const onboardingSteps: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to LoGaCo',
    subtitle: 'Local Gaming Connection',
    description: 'Connect with nearby players and enjoy multiplayer games without internet',
    icon: 'game-controller',
    color: '#00D4FF',
    features: [
      'No internet required',
      'Local multiplayer gaming',
      'Cross-platform support',
    ],
  },
  {
    id: 'connect',
    title: 'Easy Connection',
    subtitle: 'Multiple Ways to Connect',
    description: 'Use WiFi, Bluetooth, or QR codes to connect with friends instantly',
    icon: 'wifi',
    color: '#4CAF50',
    features: [
      'WiFi Direct connection',
      'Bluetooth pairing',
      'QR code scanning',
    ],
  },
  {
    id: 'games',
    title: 'Game Detection',
    subtitle: 'Automatic Game Discovery',
    description: 'We automatically detect compatible games on your device',
    icon: 'search',
    color: '#FF9800',
    features: [
      'Auto-detect installed games',
      'Compatibility checking',
      'Game recommendations',
    ],
  },
  {
    id: 'session',
    title: 'Session Management',
    subtitle: 'Host or Join Sessions',
    description: 'Create gaming sessions or join others with just a tap',
    icon: 'people',
    color: '#9C27B0',
    features: [
      'Create game sessions',
      'Invite friends easily',
      'Real-time session updates',
    ],
  },
  {
    id: 'ready',
    title: "You're All Set!",
    subtitle: 'Ready to Game',
    description: 'Start connecting with nearby players and enjoy local multiplayer gaming',
    icon: 'checkmark-circle',
    color: '#4CAF50',
    features: [
      'Scan for nearby players',
      'Join gaming sessions',
      'Have fun gaming!',
    ],
  },
];

export default function OnboardingScreen({ onComplete }: OnboardingScreenProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);
  const scrollX = useRef(new Animated.Value(0)).current;

  const handleNext = () => {
    if (currentIndex < onboardingSteps.length - 1) {
      const nextIndex = currentIndex + 1;
      setCurrentIndex(nextIndex);
      flatListRef.current?.scrollToIndex({ index: nextIndex, animated: true });
    } else {
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      const prevIndex = currentIndex - 1;
      setCurrentIndex(prevIndex);
      flatListRef.current?.scrollToIndex({ index: prevIndex, animated: true });
    }
  };

  const handleSkip = () => {
    onComplete();
  };

  const renderStep = ({ item, index }: { item: OnboardingStep; index: number }) => (
    <View style={styles.stepContainer}>
      <View style={styles.stepContent}>
        {/* Icon */}
        <View style={[styles.iconContainer, { backgroundColor: `${item.color}20` }]}>
          <Ionicons name={item.icon} size={64} color={item.color} />
        </View>

        {/* Content */}
        <View style={styles.textContent}>
          <Text style={styles.stepTitle}>{item.title}</Text>
          <Text style={[styles.stepSubtitle, { color: item.color }]}>
            {item.subtitle}
          </Text>
          <Text style={styles.stepDescription}>{item.description}</Text>
        </View>

        {/* Features */}
        <View style={styles.featuresContainer}>
          {item.features.map((feature, featureIndex) => (
            <View key={featureIndex} style={styles.featureItem}>
              <Ionicons name="checkmark" size={16} color={item.color} />
              <Text style={styles.featureText}>{feature}</Text>
            </View>
          ))}
        </View>
      </View>
    </View>
  );

  const renderPagination = () => (
    <View style={styles.pagination}>
      {onboardingSteps.map((_, index) => {
        const inputRange = [
          (index - 1) * width,
          index * width,
          (index + 1) * width,
        ];

        const dotWidth = scrollX.interpolate({
          inputRange,
          outputRange: [8, 24, 8],
          extrapolate: 'clamp',
        });

        const opacity = scrollX.interpolate({
          inputRange,
          outputRange: [0.3, 1, 0.3],
          extrapolate: 'clamp',
        });

        return (
          <Animated.View
            key={index}
            style={[
              styles.paginationDot,
              {
                width: dotWidth,
                opacity,
                backgroundColor: onboardingSteps[currentIndex].color,
              },
            ]}
          />
        );
      })}
    </View>
  );

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1a1a2e', '#16213e', '#0f3460']}
        style={styles.background}
      />

      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.skipButton}
            onPress={handleSkip}
          >
            <Text style={styles.skipText}>Skip</Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        <FlatList
          ref={flatListRef}
          data={onboardingSteps}
          renderItem={renderStep}
          keyExtractor={(item) => item.id}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={Animated.event(
            [{ nativeEvent: { contentOffset: { x: scrollX } } }],
            { useNativeDriver: false }
          )}
          onMomentumScrollEnd={(event) => {
            const index = Math.round(event.nativeEvent.contentOffset.x / width);
            setCurrentIndex(index);
          }}
          scrollEventThrottle={16}
        />

        {/* Pagination */}
        {renderPagination()}

        {/* Navigation */}
        <BlurView intensity={15} style={styles.navigationContainer}>
          <View style={styles.navigation}>
            <Button
              title="Previous"
              onPress={handlePrevious}
              variant="ghost"
              size="medium"
              disabled={currentIndex === 0}
              style={[
                styles.navButton,
                currentIndex === 0 && styles.navButtonDisabled,
              ]}
            />

            <Button
              title={currentIndex === onboardingSteps.length - 1 ? 'Get Started' : 'Next'}
              onPress={handleNext}
              variant="primary"
              size="medium"
              icon={
                currentIndex === onboardingSteps.length - 1 ? (
                  <Ionicons name="checkmark" size={20} color="#1a1a2e" />
                ) : (
                  <Ionicons name="arrow-forward" size={20} color="#1a1a2e" />
                )
              }
              style={styles.navButton}
            />
          </View>
        </BlurView>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    height: height,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  skipButton: {
    padding: 8,
  },
  skipText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    fontWeight: '500',
  },
  stepContainer: {
    width: width,
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  stepContent: {
    alignItems: 'center',
    gap: 32,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textContent: {
    alignItems: 'center',
    gap: 8,
  },
  stepTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  stepSubtitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  stepDescription: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    lineHeight: 24,
    marginTop: 8,
  },
  featuresContainer: {
    gap: 12,
    width: '100%',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingHorizontal: 16,
  },
  featureText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    flex: 1,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 20,
  },
  paginationDot: {
    height: 8,
    borderRadius: 4,
  },
  navigationContainer: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    overflow: 'hidden',
  },
  navigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: 40,
    gap: 16,
  },
  navButton: {
    flex: 1,
  },
  navButtonDisabled: {
    opacity: 0.5,
  },
});
